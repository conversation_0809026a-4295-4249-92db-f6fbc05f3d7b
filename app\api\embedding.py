from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>

from app.schemas.embedding import (
    EmbeddingCreateRequest,
    EmbeddingCreateResponse,
    SimilarityRequest,
    SimilarityResponse,
    SimilaritySearchRequest,
    SimilaritySearchResponse,
    SimilaritySearchResult,
    ClusteringRequest,
    ClusteringResponse,
    ClusterResult,
    ProviderInfoResponse,
    ProviderValidationResponse
)
from app.embedding.service import get_embedding_service
from app.embedding.provider_factory import list_available_embedding_providers
from app.embedding.base import EmbeddingRequest
from app.core.exceptions import LLMError
from app.core.dependencies import get_current_user
from app.models.user import User

router = APIRouter()
security = HTTPBearer()


@router.post("/", response_model=EmbeddingCreateResponse)
async def create_embeddings(
    request: EmbeddingCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Create embeddings for the given text(s).
    
    This endpoint accepts either a single text string or a list of texts
    and returns their corresponding embedding vectors.
    """
    try:
        service = get_embedding_service()
        
        # Create embedding request
        embedding_request = EmbeddingRequest(
            text=request.input,
            model=request.model,
            dimensions=request.dimensions,
            encoding_format=request.encoding_format,
            user=request.user or current_user.username
        )
        
        # Get embeddings
        response = await service.provider.create_embeddings(embedding_request)
        
        # Convert to API response format
        return EmbeddingCreateResponse(
            object=response.object,
            data=response.data,
            model=response.model,
            usage=response.usage
        )
        
    except LLMError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Embedding error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/similarity", response_model=SimilarityResponse)
async def compute_similarity(
    request: SimilarityRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Compute similarity between two texts.
    
    Returns a similarity score using the specified metric (cosine, dot product, or euclidean distance).
    """
    try:
        service = get_embedding_service()
        
        similarity = await service.compute_similarity(
            text1=request.text1,
            text2=request.text2,
            similarity_metric=request.similarity_metric,
            model=request.model
        )
        
        provider_info = service.get_provider_info()
        
        return SimilarityResponse(
            similarity=similarity,
            metric=request.similarity_metric,
            model=provider_info.get("model", "unknown")
        )
        
    except LLMError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Embedding error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/search", response_model=SimilaritySearchResponse)
async def similarity_search(
    request: SimilaritySearchRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Find the most similar texts to a query from a list of candidates.
    
    Returns the top-k most similar texts ranked by similarity score.
    """
    try:
        service = get_embedding_service()
        
        results = await service.find_most_similar(
            query_text=request.query,
            candidate_texts=request.candidates,
            top_k=request.top_k,
            similarity_metric=request.similarity_metric,
            model=request.model
        )
        
        provider_info = service.get_provider_info()
        
        search_results = [
            SimilaritySearchResult(
                text=text,
                similarity=similarity,
                index=index
            )
            for text, similarity, index in results
        ]
        
        return SimilaritySearchResponse(
            query=request.query,
            results=search_results,
            metric=request.similarity_metric,
            model=provider_info.get("model", "unknown")
        )
        
    except LLMError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Embedding error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/cluster", response_model=ClusteringResponse)
async def cluster_texts(
    request: ClusteringRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Cluster texts using K-means clustering on their embeddings.
    
    Groups similar texts together and returns cluster assignments.
    Requires scikit-learn to be installed.
    """
    try:
        service = get_embedding_service()
        
        clusters = await service.cluster_texts(
            texts=request.texts,
            n_clusters=request.n_clusters,
            model=request.model
        )
        
        provider_info = service.get_provider_info()
        
        cluster_results = []
        for cluster_id, texts_with_indices in clusters.items():
            cluster_texts = [
                {"text": text, "original_index": index}
                for text, index in texts_with_indices
            ]
            cluster_results.append(ClusterResult(
                cluster_id=cluster_id,
                texts=cluster_texts
            ))
        
        return ClusteringResponse(
            clusters=cluster_results,
            n_clusters=request.n_clusters,
            model=provider_info.get("model", "unknown")
        )
        
    except LLMError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Embedding error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/provider/info", response_model=ProviderInfoResponse)
async def get_provider_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get information about the current embedding provider and available providers.
    """
    try:
        service = get_embedding_service()
        provider_info = service.get_provider_info()
        available_providers = list_available_embedding_providers()
        
        return ProviderInfoResponse(
            provider=provider_info.get("provider", "unknown"),
            model=provider_info.get("model", "unknown"),
            config=provider_info.get("config", {}),
            available_providers=available_providers
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/provider/validate", response_model=ProviderValidationResponse)
async def validate_provider(
    current_user: User = Depends(get_current_user)
):
    """
    Validate that the current embedding provider is working correctly.
    """
    try:
        service = get_embedding_service()
        provider_info = service.get_provider_info()
        
        is_valid = await service.validate_provider()
        
        return ProviderValidationResponse(
            valid=is_valid,
            provider=provider_info.get("provider", "unknown"),
            model=provider_info.get("model", "unknown"),
            error=None if is_valid else "Provider validation failed"
        )
        
    except Exception as e:
        return ProviderValidationResponse(
            valid=False,
            provider="unknown",
            model="unknown",
            error=str(e)
        )
