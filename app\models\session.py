import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from .base import Base


class Session(Base):
    """Session model for storing user chat sessions."""
    
    __tablename__ = "sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    session_key = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)  # User-friendly session name
    role_name = Column(String(50), nullable=False)  # AI role for this session
    context = Column(JSON, default={})  # Additional context data
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_accessed = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    messages = relationship("Message", back_populates="session", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Session(id={self.id}, name='{self.name}', role='{self.role_name}')>"
    
    def to_dict(self):
        """Convert session to dictionary."""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "session_key": self.session_key,
            "name": self.name,
            "role_name": self.role_name,
            "context": self.context,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat(),
            "last_accessed": self.last_accessed.isoformat()
        }
    
    def generate_session_key(self) -> str:
        """Generate a unique session key."""
        return f"sess_{uuid.uuid4().hex[:16]}"