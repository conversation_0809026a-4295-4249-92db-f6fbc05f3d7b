from abc import ABC, abstractmethod
from typing import List, Dict, Any, AsyncGenerator, Optional, Union
from pydantic import BaseModel
import uuid


class FunctionCall(BaseModel):
    """Function call from LLM."""
    name: str
    arguments: str  # JSON string of arguments


class ToolCall(BaseModel):
    """Tool call from LLM (OpenAI format)."""
    id: str
    type: str = "function"
    function: FunctionCall


class ChatMessage(BaseModel):
    """Standard chat message format for LLM providers."""
    role: str  # 'system', 'user', 'assistant', 'function', 'tool'
    content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    # Function calling fields
    function_call: Optional[FunctionCall] = None  # Legacy OpenAI format
    tool_calls: Optional[List[ToolCall]] = None   # New OpenAI format
    tool_call_id: Optional[str] = None            # For tool response messages
    name: Optional[str] = None                    # Function name for function role


class LLMResponse(BaseModel):
    """Standard LLM response format."""
    content: Optional[str] = None
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None
    # Function calling fields
    function_call: Optional[FunctionCall] = None
    tool_calls: Optional[List[ToolCall]] = None


class StreamingChunk(BaseModel):
    """Streaming response chunk."""
    content: Optional[str] = None
    is_complete: bool = False
    metadata: Optional[Dict[str, Any]] = None
    # Function calling fields
    function_call: Optional[FunctionCall] = None
    tool_calls: Optional[List[ToolCall]] = None


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    @abstractmethod
    async def generate_response(
        self,
        messages: List[ChatMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a complete response from the LLM.

        Args:
            messages: List of chat messages
            tools: Optional list of tools/functions available for calling
            **kwargs: Additional provider-specific parameters

        Returns:
            LLMResponse: Complete response from the LLM
        """
        pass
    
    @abstractmethod
    async def generate_stream(
        self,
        messages: List[ChatMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Generate a streaming response from the LLM.

        Args:
            messages: List of chat messages
            tools: Optional list of tools/functions available for calling
            **kwargs: Additional provider-specific parameters

        Yields:
            StreamingChunk: Streaming response chunks
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """
        Validate that the provider connection is working.
        
        Returns:
            bool: True if connection is valid
        """
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        return {
            "provider": self.__class__.__name__,
            "model": self.config.get("model", "unknown"),
            "config": {k: v for k, v in self.config.items() if k != "api_key"}
        }


class LLMProviderFactory:
    """Factory for creating LLM providers."""
    
    _providers = {}
    
    @classmethod
    def register_provider(cls, name: str, provider_class: type):
        """Register a new LLM provider."""
        cls._providers[name] = provider_class
    
    @classmethod
    def create_provider(cls, name: str, config: Dict[str, Any]) -> LLMProvider:
        """Create an LLM provider instance."""
        if name not in cls._providers:
            raise ValueError(f"Unknown LLM provider: {name}")
        
        return cls._providers[name](config)
    
    @classmethod
    def list_providers(cls) -> List[str]:
        """List available provider names."""
        return list(cls._providers.keys())