import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from functools import lru_cache

from app.embedding.base import EmbeddingProvider, EmbeddingRequest
from app.embedding.provider_factory import create_embedding_provider
from app.core.exceptions import LLMError


class EmbeddingService:
    """High-level service for embedding operations."""
    
    def __init__(self, provider: Optional[EmbeddingProvider] = None):
        """
        Initialize embedding service.
        
        Args:
            provider: Optional embedding provider. If None, creates default provider.
        """
        self._provider = provider
    
    @property
    def provider(self) -> EmbeddingProvider:
        """Get or create the embedding provider."""
        if self._provider is None:
            self._provider = create_embedding_provider()
        return self._provider
    
    async def embed_text(
        self, 
        text: str, 
        model: Optional[str] = None,
        **kwargs
    ) -> List[float]:
        """
        Embed a single text.
        
        Args:
            text: Text to embed
            model: Optional model override
            **kwargs: Additional parameters
            
        Returns:
            List[float]: Embedding vector
        """
        request = EmbeddingRequest(text=text, model=model, **kwargs)
        response = await self.provider.create_embeddings(request)
        return response.get_embedding(0)
    
    async def embed_texts(
        self, 
        texts: List[str], 
        model: Optional[str] = None,
        batch_size: int = 100,
        **kwargs
    ) -> List[List[float]]:
        """
        Embed multiple texts with optional batching.
        
        Args:
            texts: List of texts to embed
            model: Optional model override
            batch_size: Maximum texts per batch
            **kwargs: Additional parameters
            
        Returns:
            List[List[float]]: List of embedding vectors
        """
        if len(texts) <= batch_size:
            # Single batch
            request = EmbeddingRequest(text=texts, model=model, **kwargs)
            response = await self.provider.create_embeddings(request)
            return response.get_embeddings()
        
        # Multiple batches
        all_embeddings = []
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            request = EmbeddingRequest(text=batch, model=model, **kwargs)
            response = await self.provider.create_embeddings(request)
            all_embeddings.extend(response.get_embeddings())
        
        return all_embeddings
    
    async def compute_similarity(
        self,
        text1: str,
        text2: str,
        similarity_metric: str = "cosine",
        model: Optional[str] = None,
        **kwargs
    ) -> float:
        """
        Compute similarity between two texts.
        
        Args:
            text1: First text
            text2: Second text
            similarity_metric: Similarity metric ("cosine", "dot", "euclidean")
            model: Optional model override
            **kwargs: Additional parameters
            
        Returns:
            float: Similarity score
        """
        embeddings = await self.embed_texts([text1, text2], model=model, **kwargs)
        return self._compute_similarity_score(
            embeddings[0], 
            embeddings[1], 
            similarity_metric
        )
    
    async def find_most_similar(
        self,
        query_text: str,
        candidate_texts: List[str],
        top_k: int = 5,
        similarity_metric: str = "cosine",
        model: Optional[str] = None,
        **kwargs
    ) -> List[Tuple[str, float, int]]:
        """
        Find most similar texts to a query.
        
        Args:
            query_text: Query text
            candidate_texts: List of candidate texts
            top_k: Number of top results to return
            similarity_metric: Similarity metric
            model: Optional model override
            **kwargs: Additional parameters
            
        Returns:
            List[Tuple[str, float, int]]: List of (text, similarity_score, original_index)
        """
        # Embed query and candidates
        all_texts = [query_text] + candidate_texts
        embeddings = await self.embed_texts(all_texts, model=model, **kwargs)
        
        query_embedding = embeddings[0]
        candidate_embeddings = embeddings[1:]
        
        # Compute similarities
        similarities = []
        for i, candidate_embedding in enumerate(candidate_embeddings):
            similarity = self._compute_similarity_score(
                query_embedding, 
                candidate_embedding, 
                similarity_metric
            )
            similarities.append((candidate_texts[i], similarity, i))
        
        # Sort by similarity (descending) and return top_k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    async def cluster_texts(
        self,
        texts: List[str],
        n_clusters: int,
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[int, List[Tuple[str, int]]]:
        """
        Cluster texts using K-means clustering on embeddings.
        
        Args:
            texts: List of texts to cluster
            n_clusters: Number of clusters
            model: Optional model override
            **kwargs: Additional parameters
            
        Returns:
            Dict[int, List[Tuple[str, int]]]: Clusters mapped to (text, original_index) pairs
        """
        try:
            from sklearn.cluster import KMeans
        except ImportError:
            raise LLMError("scikit-learn is required for clustering. Install with: pip install scikit-learn")
        
        # Get embeddings
        embeddings = await self.embed_texts(texts, model=model, **kwargs)
        
        # Perform clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(embeddings)
        
        # Group by clusters
        clusters = {}
        for i, (text, label) in enumerate(zip(texts, cluster_labels)):
            if label not in clusters:
                clusters[label] = []
            clusters[label].append((text, i))
        
        return clusters
    
    def _compute_similarity_score(
        self, 
        embedding1: List[float], 
        embedding2: List[float], 
        metric: str
    ) -> float:
        """
        Compute similarity score between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            metric: Similarity metric
            
        Returns:
            float: Similarity score
        """
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        if metric == "cosine":
            # Cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            return dot_product / (norm1 * norm2)
        
        elif metric == "dot":
            # Dot product
            return np.dot(vec1, vec2)
        
        elif metric == "euclidean":
            # Negative Euclidean distance (higher = more similar)
            return -np.linalg.norm(vec1 - vec2)
        
        else:
            raise ValueError(f"Unknown similarity metric: {metric}")
    
    async def validate_provider(self) -> bool:
        """
        Validate that the embedding provider is working.
        
        Returns:
            bool: True if provider is valid
        """
        return await self.provider.validate_connection()
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        Get information about the current provider.
        
        Returns:
            Dict[str, Any]: Provider information
        """
        return self.provider.get_model_info()


@lru_cache()
def get_embedding_service() -> EmbeddingService:
    """
    Get cached embedding service instance.
    
    Returns:
        EmbeddingService: Cached service instance
    """
    return EmbeddingService()
