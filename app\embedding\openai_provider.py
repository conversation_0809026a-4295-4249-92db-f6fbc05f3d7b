from typing import Dict, Any, Optional, List, Union
from openai import Async<PERSON>penA<PERSON>

from app.embedding.base import (
    EmbeddingProvider,
    EmbeddingRequest,
    EmbeddingResponse,
    EmbeddingData,
    EmbeddingUsage,
    EmbeddingProviderFactory
)
from app.core.config import settings
from app.core.exceptions import LLMError


class OpenAIEmbeddingProvider(EmbeddingProvider):
    """OpenAI embedding provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Initialize OpenAI client
        api_key = config.get("api_key") or settings.openai_api_key
        if not api_key:
            raise LLMError("OpenAI API key is required for embeddings")
        
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = config.get("model", settings.openai_embedding_model)
    
    async def create_embeddings(
        self,
        request: EmbeddingRequest,
        **kwargs
    ) -> EmbeddingResponse:
        """
        Create embeddings using OpenAI.
        
        Args:
            request: Embedding request containing text and parameters
            **kwargs: Additional parameters
            
        Returns:
            EmbeddingResponse: Response containing embeddings and metadata
            
        Raises:
            LLMError: If the API call fails
        """
        try:
            # Prepare input text
            input_text = request.text
            if isinstance(input_text, str):
                input_text = [input_text]
            
            # Prepare parameters
            params = {
                "model": request.model or self.model,
                "input": input_text,
                "encoding_format": request.encoding_format
            }
            
            # Add optional parameters
            if request.dimensions:
                params["dimensions"] = request.dimensions
            if request.user:
                params["user"] = request.user
            
            # Override with any provided kwargs
            params.update(kwargs)
            
            # Make API call
            response = await self.client.embeddings.create(**params)
            
            # Convert to our response format
            embedding_data = []
            for i, item in enumerate(response.data):
                embedding_data.append(EmbeddingData(
                    embedding=item.embedding,
                    index=i
                ))
            
            usage = EmbeddingUsage(
                prompt_tokens=response.usage.prompt_tokens,
                total_tokens=response.usage.total_tokens
            )
            
            return EmbeddingResponse(
                data=embedding_data,
                model=response.model,
                usage=usage
            )
            
        except Exception as e:
            if "openai" in str(type(e)).lower():
                raise LLMError(f"OpenAI embedding API error: {str(e)}")
            raise LLMError(f"Unexpected error in OpenAI embeddings: {str(e)}")
    
    async def validate_connection(self) -> bool:
        """
        Validate OpenAI embedding connection by making a simple API call.
        
        Returns:
            bool: True if connection is valid
        """
        try:
            # Make a minimal API call to test connection
            test_request = EmbeddingRequest(text="test")
            await self.create_embeddings(test_request)
            return True
            
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current embedding model."""
        return {
            "provider": self.__class__.__name__,
            "model": self.model,
            "config": {k: v for k, v in self.config.items() if k not in ["api_key"]}
        }


# Register the OpenAI embedding provider
EmbeddingProviderFactory.register_provider("openai", OpenAIEmbeddingProvider)


def create_openai_embedding_provider(config: Optional[Dict[str, Any]] = None) -> OpenAIEmbeddingProvider:
    """
    Convenience function to create an OpenAI embedding provider.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        OpenAIEmbeddingProvider: Configured OpenAI embedding provider instance
    """
    if config is None:
        config = {
            "model": settings.openai_embedding_model,
            "api_key": settings.openai_api_key
        }
    
    return OpenAIEmbeddingProvider(config)
