"""
User database tools for retrieving user information from external PostgreSQL database.

This module contains tools for querying user data from an external PostgreSQL database
containing user profiles with first_name, last_name, preferred_name, title, and bio.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
import asyncpg
from contextlib import asynccontextmanager

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry
from app.core.config import settings

logger = logging.getLogger(__name__)


class UserDatabaseTool(BaseTool):
    """Tool for retrieving user information from external PostgreSQL database."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self._connection_pool = None
        self.table_name = self.config.get("table_name", "user")

    async def get_pool(self):
        """Get or create a database connection pool."""
        if self._connection_pool is None:
            if not settings.external_user_db_url:
                raise ValueError("External user database URL not configured. Please set EXTERNAL_USER_DB_URL environment variable.")
            try:
                self._connection_pool = await asyncpg.create_pool(settings.external_user_db_url)
            except Exception as e:
                logger.error(f"Failed to create database connection pool: {str(e)}")
                raise
        return self._connection_pool

    @asynccontextmanager
    async def get_connection(self):
        """Get a database connection from the pool."""
        pool = await self.get_pool()
        connection = None
        try:
            connection = await pool.acquire()
            yield connection
        finally:
            if connection:
                await pool.release(connection)

    @property
    def name(self) -> str:
        return "user_database_lookup"

    @property
    def description(self) -> str:
        return "Retrieve user information from external PostgreSQL database including name, title, and bio"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "search_value": {
                        "type": "string",
                        "description": "Value to search for (name, email, or title)."
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of results to return",
                        "default": 1,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["search_value"]
            }
        )

    async def execute(self, **kwargs) -> ToolResult:
        """
        Retrieve user information from external database.
        
        Args:
            search_value: Value to search for
            limit: Maximum number of results
            
        Returns:
            ToolResult with user information
        """
        try:
            search_value = kwargs.get("search_value", "").strip()
            limit = kwargs.get("limit", 1)

            if not search_value:
                return ToolResult(
                    success=False,
                    error="search_value is required"
                )

            # Use a tsquery for full-text search
            # We'll search for the value as a whole phrase and also with terms OR'd together
            ts_query = f"to_tsquery('english', '{' & '.join(search_value.split())}')"

            query = f"""
                SELECT 
                    id,
                    first_name,
                    last_name,
                    preferred_name,
                    title,
                    email,
                    bio,
                    COALESCE(preferred_name, CONCAT(first_name, ' ', last_name)) as display_name,
                    ts_rank(
                        to_tsvector('english', COALESCE(first_name, '') || ' ' || COALESCE(last_name, '') || ' ' || COALESCE(preferred_name, '') || ' ' || COALESCE(title, '') || ' ' || COALESCE(email, '')),
                        {ts_query}
                    ) as rank
                FROM "{self.table_name}"
                WHERE to_tsvector('english', COALESCE(first_name, '') || ' ' || COALESCE(last_name, '') || ' ' || COALESCE(preferred_name, '') || ' ' || COALESCE(title, '') || ' ' || COALESCE(email, '')) @@ {ts_query}
                ORDER BY rank DESC
                LIMIT $1
            """

            async with self.get_connection() as conn:
                rows = await conn.fetch(query, limit)
                users = [dict(row) for row in rows]
                
                return ToolResult(
                    success=True,
                    result={
                        "search_value": search_value,
                        "users": users,
                        "total_found": len(users),
                        "limit_applied": limit
                    },
                    metadata={
                        "operation": "user_database_lookup",
                        "result_count": len(users)
                    }
                )

        except ValueError as e:
            logger.error(f"User database lookup validation error: {str(e)}")
            return ToolResult(success=False, error=str(e))
        except Exception as e:
            logger.error(f"User database lookup error: {str(e)}")
            return ToolResult(success=False, error=f"Database lookup failed: {str(e)}")


# Register tools
tool_registry.register_tool(UserDatabaseTool())
