from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from dataclasses import dataclass


@dataclass
class EmbeddingRequest:
    """Request for text embedding."""
    text: Union[str, List[str]]
    model: Optional[str] = None
    dimensions: Optional[int] = None
    encoding_format: str = "float"
    user: Optional[str] = None


class EmbeddingData(BaseModel):
    """Individual embedding data."""
    object: str = "embedding"
    embedding: List[float]
    index: int


class EmbeddingUsage(BaseModel):
    """Token usage information for embedding request."""
    prompt_tokens: int
    total_tokens: int


class EmbeddingResponse(BaseModel):
    """Response from embedding provider."""
    object: str = "list"
    data: List[EmbeddingData]
    model: str
    usage: EmbeddingUsage
    
    def get_embeddings(self) -> List[List[float]]:
        """Get list of embeddings from response."""
        return [item.embedding for item in self.data]
    
    def get_embedding(self, index: int = 0) -> List[float]:
        """Get single embedding by index."""
        if index >= len(self.data):
            raise IndexError(f"Embedding index {index} out of range")
        return self.data[index].embedding


class EmbeddingProvider(ABC):
    """Abstract base class for embedding providers."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    @abstractmethod
    async def create_embeddings(
        self,
        request: EmbeddingRequest,
        **kwargs
    ) -> EmbeddingResponse:
        """
        Create embeddings for the given text(s).
        
        Args:
            request: Embedding request containing text and parameters
            **kwargs: Additional provider-specific parameters
            
        Returns:
            EmbeddingResponse: Response containing embeddings and metadata
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """
        Validate that the provider connection is working.
        
        Returns:
            bool: True if connection is valid
        """
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        return {
            "provider": self.__class__.__name__,
            "model": self.config.get("model", "unknown"),
            "config": {k: v for k, v in self.config.items() if k not in ["api_key", "api_secret"]}
        }
    
    async def embed_text(self, text: str, **kwargs) -> List[float]:
        """
        Convenience method to embed a single text.
        
        Args:
            text: Text to embed
            **kwargs: Additional parameters
            
        Returns:
            List[float]: Embedding vector
        """
        request = EmbeddingRequest(text=text, **kwargs)
        response = await self.create_embeddings(request)
        return response.get_embedding(0)
    
    async def embed_texts(self, texts: List[str], **kwargs) -> List[List[float]]:
        """
        Convenience method to embed multiple texts.
        
        Args:
            texts: List of texts to embed
            **kwargs: Additional parameters
            
        Returns:
            List[List[float]]: List of embedding vectors
        """
        request = EmbeddingRequest(text=texts, **kwargs)
        response = await self.create_embeddings(request)
        return response.get_embeddings()


class EmbeddingProviderFactory:
    """Factory for creating embedding providers."""
    
    _providers = {}
    
    @classmethod
    def register_provider(cls, name: str, provider_class: type):
        """Register a new embedding provider."""
        cls._providers[name] = provider_class
    
    @classmethod
    def create_provider(cls, name: str, config: Dict[str, Any]) -> EmbeddingProvider:
        """Create an embedding provider instance."""
        if name not in cls._providers:
            raise ValueError(f"Unknown embedding provider: {name}")
        
        return cls._providers[name](config)
    
    @classmethod
    def list_providers(cls) -> List[str]:
        """List available provider names."""
        return list(cls._providers.keys())
