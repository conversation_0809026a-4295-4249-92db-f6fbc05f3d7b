from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.orm import Session
from typing import Optional

from app.models.base import get_db
from app.models.user import User
from app.schemas.session import (
    SessionCreate, 
    SessionResponse, 
    SessionUpdate, 
    SessionListResponse,
    SessionContext
)
from app.schemas.chat import RoleR<PERSON>ponse, RoleListResponse
from app.session.manager import Session<PERSON>anager
from app.core.dependencies import get_anonymous_user
from app.core.exceptions import NotFoundError
from app.agent.role_loader import role_loader

router = APIRouter()


@router.post("/", response_model=SessionResponse, status_code=status.HTTP_201_CREATED)
async def create_session(
    session_data: SessionCreate,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Create a new session for the current user (including anonymous users).

    Args:
        session_data: Session creation data (name is optional)
        current_user: Current user (authenticated or anonymous)
        db: Database session

    Returns:
        SessionResponse: Created session information
    """
    session_manager = SessionManager(db)
    return await session_manager.create_session(current_user.id, session_data)


@router.get("/", response_model=SessionListResponse)
async def list_sessions(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Number of sessions per page"),
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    List all active sessions for the current user.
    
    Args:
        page: Page number (1-based)
        page_size: Number of sessions per page
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SessionListResponse: List of sessions with pagination info
    """
    session_manager = SessionManager(db)
    result = await session_manager.list_user_sessions(
        current_user.id, 
        page=page, 
        page_size=page_size
    )
    
    return SessionListResponse(**result)


@router.get("/{session_key}", response_model=SessionResponse)
async def get_session(
    session_key: str,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific session by key.

    Args:
        session_key: Session key
        current_user: Current user (authenticated or anonymous)
        db: Database session

    Returns:
        SessionResponse: Session information

    Raises:
        NotFoundError: If session not found or not owned by user
    """
    session_manager = SessionManager(db)
    session = await session_manager.get_session(session_key, current_user.id)

    if not session:
        raise NotFoundError("Session not found")

    return session


@router.put("/{session_key}", response_model=SessionResponse)
async def update_session(
    session_key: str,
    update_data: SessionUpdate,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Update a session (currently only supports name changes).

    Args:
        session_key: Session key
        update_data: Update data
        current_user: Current user (authenticated or anonymous)
        db: Database session

    Returns:
        SessionResponse: Updated session information
    """
    session_manager = SessionManager(db)
    return await session_manager.update_session(session_key, current_user.id, update_data)


@router.delete("/{session_key}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_session(
    session_key: str,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Delete a session and all its data.

    Args:
        session_key: Session key
        current_user: Current user (authenticated or anonymous)
        db: Database session

    Raises:
        NotFoundError: If session not found or not owned by user
    """
    session_manager = SessionManager(db)
    await session_manager.delete_session(session_key, current_user.id)


@router.get("/{session_key}/context", response_model=SessionContext)
async def get_session_context(
    session_key: str,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Get session context and metadata.

    Args:
        session_key: Session key
        current_user: Current user (authenticated or anonymous)
        db: Database session

    Returns:
        SessionContext: Session context data

    Raises:
        NotFoundError: If session not found or not owned by user
    """
    session_manager = SessionManager(db)

    # First verify user owns the session
    session = await session_manager.get_session(session_key, current_user.id)
    if not session:
        raise NotFoundError("Session not found")

    # Get context
    context = await session_manager.get_session_context(session_key)
    if not context:
        raise NotFoundError("Session context not found")

    return context


@router.post("/{session_key}/activity", status_code=status.HTTP_204_NO_CONTENT)
async def update_session_activity(
    session_key: str,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Update session last activity timestamp.

    Args:
        session_key: Session key
        current_user: Current user (authenticated or anonymous)
        db: Database session

    Raises:
        NotFoundError: If session not found or not owned by user
    """
    session_manager = SessionManager(db)

    # Verify user owns the session
    session = await session_manager.get_session(session_key, current_user.id)
    if not session:
        raise NotFoundError("Session not found")

    await session_manager.update_session_activity(session_key)


@router.get("/roles/", response_model=RoleListResponse)
async def list_available_roles():
    """
    List all available AI roles.
    
    Returns:
        RoleListResponse: List of available roles
    """
    active_roles = role_loader.get_active_roles()
    
    roles = []
    for role_name, role_config in active_roles.items():
        roles.append(RoleResponse(
            name=role_name,
            display_name=role_config.display_name,
            description=role_config.description,
            tools=role_config.tools,
            is_active=role_config.is_active
        ))
    
    return RoleListResponse(roles=roles)


@router.get("/roles/{role_name}", response_model=RoleResponse)
async def get_role_details(role_name: str):
    """
    Get details for a specific role.
    
    Args:
        role_name: Name of the role
        
    Returns:
        RoleResponse: Role details
        
    Raises:
        NotFoundError: If role not found
    """
    role_config = role_loader.get_role(role_name)
    
    if not role_config or not role_config.is_active:
        raise NotFoundError(f"Role '{role_name}' not found")
    
    return RoleResponse(
        name=role_name,
        display_name=role_config.display_name,
        description=role_config.description,
        tools=role_config.tools,
        is_active=role_config.is_active
    )