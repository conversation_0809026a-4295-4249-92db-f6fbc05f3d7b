from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from typing import List, Optional
import logging

from app.models.base import get_db
from app.models.user import User
from app.models.role import Role
from app.schemas.role import (
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    RoleListResponse
)
from app.core.dependencies import get_current_active_user
from app.core.exceptions import NotFoundError, ConflictError, ValidationError
from app.agent.role_loader import role_loader

logger = logging.getLogger(__name__)

router = APIRouter()


# Helper functions integrated from RoleService
def get_role_by_name(db: Session, name: str) -> Optional[Role]:
    """Get a role by name."""
    return db.query(Role).filter(Role.name == name).first()


def get_role_by_id(db: Session, role_id: str) -> Optional[Role]:
    """Get a role by ID."""
    return db.query(Role).filter(Role.id == role_id).first()


def list_roles_from_db(db: Session, active_only: bool = True) -> List[Role]:
    """List all roles, optionally filtering by active status."""
    query = db.query(Role)
    if active_only:
        query = query.filter(Role.is_active == True)
    return query.order_by(Role.name).all()


def create_role_in_db(db: Session, role_data: RoleCreate) -> Role:
    """Create a new role."""
    # Check if role with same name already exists
    existing_role = get_role_by_name(db, role_data.name)
    if existing_role:
        raise ConflictError(f"Role with name '{role_data.name}' already exists")

    try:
        db_role = Role(
            name=role_data.name,
            display_name=role_data.display_name,
            description=role_data.description,
            system_prompt=role_data.system_prompt,
            tools=role_data.tools,
            config=role_data.config,
            is_active=role_data.is_active
        )
        db.add(db_role)
        db.commit()
        db.refresh(db_role)
        logger.info(f"Created role: {role_data.name}")
        return db_role
    except IntegrityError as e:
        db.rollback()
        raise ConflictError(f"Role creation failed: {str(e)}")


def update_role_in_db(db: Session, name: str, role_data: RoleUpdate) -> Optional[Role]:
    """Update an existing role."""
    role = get_role_by_name(db, name)
    if not role:
        raise NotFoundError(f"Role '{name}' not found")

    try:
        # Update only provided fields
        update_data = role_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(role, key, value)

        db.commit()
        db.refresh(role)
        logger.info(f"Updated role: {name}")
        return role
    except IntegrityError as e:
        db.rollback()
        raise ConflictError(f"Role update failed: {str(e)}")


def delete_role_in_db(db: Session, name: str, hard_delete: bool = False) -> bool:
    """Delete a role (soft delete by default, hard delete if specified)."""
    role = get_role_by_name(db, name)
    if not role:
        raise NotFoundError(f"Role '{name}' not found")

    if hard_delete:
        db.delete(role)
        db.commit()
        logger.info(f"Permanently deleted role: {name}")
    else:
        role.is_active = False
        db.commit()
        logger.info(f"Deactivated role: {name}")

    return True


@router.get("/", response_model=RoleListResponse)
async def list_roles(
    active_only: bool = Query(True, description="Filter by active roles only"),
    db: Session = Depends(get_db)
):
    """
    List all roles from database.

    Args:
        active_only: Whether to return only active roles
        db: Database session

    Returns:
        RoleListResponse: List of roles
    """
    roles = list_roles_from_db(db, active_only=active_only)

    role_responses = []
    for role in roles:
        role_responses.append(RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        ))

    return RoleListResponse(roles=role_responses, total=len(role_responses))


@router.get("/{role_name}", response_model=RoleResponse)
async def get_role(
    role_name: str,
    db: Session = Depends(get_db)
):
    """
    Get a specific role by name from database.

    Args:
        role_name: Name of the role
        db: Database session

    Returns:
        RoleResponse: Role details

    Raises:
        HTTPException: If role not found
    """
    role = get_role_by_name(db, role_name)

    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Role '{role_name}' not found"
        )

    return RoleResponse(
        id=str(role.id),
        name=role.name,
        display_name=role.display_name,
        description=role.description or "",
        system_prompt=role.system_prompt,
        tools=role.tools or [],
        config=role.config or {},
        is_active=role.is_active,
        created_at=role.created_at,
        updated_at=role.updated_at
    )


@router.post("/", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    role_data: RoleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new role in database (admin only).

    Args:
        role_data: Role creation data
        db: Database session
        current_user: Current authenticated user

    Returns:
        RoleResponse: Created role

    Raises:
        HTTPException: If role creation fails
    """
    try:
        role = create_role_in_db(db, role_data)

        # Reload roles in the role loader to include the new role
        role_loader.set_database_session(db)

        return RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.put("/{role_name}", response_model=RoleResponse)
async def update_role(
    role_name: str,
    role_data: RoleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update an existing role in database (admin only).

    Args:
        role_name: Name of the role to update
        role_data: Role update data
        db: Database session
        current_user: Current authenticated user

    Returns:
        RoleResponse: Updated role

    Raises:
        HTTPException: If role update fails
    """
    try:
        role = update_role_in_db(db, role_name, role_data)

        # Reload roles in the role loader to reflect changes
        role_loader.set_database_session(db)

        return RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.delete("/{role_name}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_name: str,
    hard_delete: bool = Query(False, description="Permanently delete role instead of soft delete"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a role from database (admin only).

    Args:
        role_name: Name of the role to delete
        hard_delete: Whether to permanently delete the role
        db: Database session
        current_user: Current authenticated user

    Raises:
        HTTPException: If role deletion fails
    """
    try:
        delete_role_in_db(db, role_name, hard_delete=hard_delete)

        # Reload roles in the role loader to reflect changes
        role_loader.set_database_session(db)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
