from typing import List, Optional, Union, Dict, Any, Tuple
from pydantic import BaseModel, Field


class EmbeddingCreateRequest(BaseModel):
    """Request schema for creating embeddings."""
    input: Union[str, List[str]] = Field(..., description="Text(s) to embed")
    model: Optional[str] = Field(None, description="Model to use for embedding")
    encoding_format: str = Field("float", description="Encoding format for embeddings")
    dimensions: Optional[int] = Field(None, description="Number of dimensions for embedding")
    user: Optional[str] = Field(None, description="User identifier for tracking")


class EmbeddingData(BaseModel):
    """Individual embedding data."""
    object: str = "embedding"
    embedding: List[float]
    index: int


class EmbeddingUsage(BaseModel):
    """Usage information for embedding request."""
    prompt_tokens: int
    total_tokens: int


class EmbeddingCreateResponse(BaseModel):
    """Response schema for embedding creation."""
    object: str = "list"
    data: List[EmbeddingData]
    model: str
    usage: EmbeddingUsage


class SimilarityRequest(BaseModel):
    """Request schema for similarity computation."""
    text1: str = Field(..., description="First text")
    text2: str = Field(..., description="Second text")
    model: Optional[str] = Field(None, description="Model to use for embedding")
    similarity_metric: str = Field("cosine", description="Similarity metric (cosine, dot, euclidean)")


class SimilarityResponse(BaseModel):
    """Response schema for similarity computation."""
    similarity: float = Field(..., description="Similarity score")
    metric: str = Field(..., description="Similarity metric used")
    model: str = Field(..., description="Model used for embeddings")


class SimilaritySearchRequest(BaseModel):
    """Request schema for similarity search."""
    query: str = Field(..., description="Query text")
    candidates: List[str] = Field(..., description="Candidate texts to search")
    top_k: int = Field(5, description="Number of top results to return")
    model: Optional[str] = Field(None, description="Model to use for embedding")
    similarity_metric: str = Field("cosine", description="Similarity metric (cosine, dot, euclidean)")


class SimilaritySearchResult(BaseModel):
    """Individual similarity search result."""
    text: str = Field(..., description="Candidate text")
    similarity: float = Field(..., description="Similarity score")
    index: int = Field(..., description="Original index in candidates list")


class SimilaritySearchResponse(BaseModel):
    """Response schema for similarity search."""
    query: str = Field(..., description="Original query text")
    results: List[SimilaritySearchResult] = Field(..., description="Search results")
    metric: str = Field(..., description="Similarity metric used")
    model: str = Field(..., description="Model used for embeddings")


class ClusteringRequest(BaseModel):
    """Request schema for text clustering."""
    texts: List[str] = Field(..., description="Texts to cluster")
    n_clusters: int = Field(..., description="Number of clusters")
    model: Optional[str] = Field(None, description="Model to use for embedding")


class ClusterResult(BaseModel):
    """Individual cluster result."""
    cluster_id: int = Field(..., description="Cluster identifier")
    texts: List[Dict[str, Union[str, int]]] = Field(..., description="Texts in cluster with original indices")


class ClusteringResponse(BaseModel):
    """Response schema for text clustering."""
    clusters: List[ClusterResult] = Field(..., description="Clustering results")
    n_clusters: int = Field(..., description="Number of clusters")
    model: str = Field(..., description="Model used for embeddings")


class ProviderInfoResponse(BaseModel):
    """Response schema for provider information."""
    provider: str = Field(..., description="Provider class name")
    model: str = Field(..., description="Current model")
    config: Dict[str, Any] = Field(..., description="Provider configuration (excluding sensitive data)")
    available_providers: Dict[str, str] = Field(..., description="Available providers with descriptions")


class ProviderValidationResponse(BaseModel):
    """Response schema for provider validation."""
    valid: bool = Field(..., description="Whether provider connection is valid")
    provider: str = Field(..., description="Provider class name")
    model: str = Field(..., description="Current model")
    error: Optional[str] = Field(None, description="Error message if validation failed")
