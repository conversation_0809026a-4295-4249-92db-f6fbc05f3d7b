from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pydantic import BaseModel


class ToolSchema(BaseModel):
    """Schema for tool definition."""
    name: str
    description: str
    parameters: Dict[str, Any]


class ToolResult(BaseModel):
    """Result from tool execution."""
    success: bool
    result: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = {}


class BaseTool(ABC):
    """Abstract base class for AI agent tools."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Tool name identifier."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Tool description."""
        pass
    
    @abstractmethod
    def get_schema(self) -> ToolSchema:
        """Get tool schema for LLM function calling."""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute the tool with given parameters.
        
        Args:
            **kwargs: Tool parameters
            
        Returns:
            ToolResult: Execution result
        """
        pass
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        Validate tool parameters against schema.
        
        Args:
            parameters: Parameters to validate
            
        Returns:
            bool: True if valid
        """
        schema = self.get_schema()
        
        # Check required parameters
        for required_param in schema.required:
            if required_param not in parameters:
                return False
        
        return True


class ToolRegistry:
    """Registry for managing available tools."""
    
    def __init__(self):
        self._tools: Dict[str, BaseTool] = {}
    
    def register_tool(self, tool: BaseTool):
        """Register a tool."""
        self._tools[tool.name] = tool
    
    def get_tool(self, name: str) -> Optional[BaseTool]:
        """Get a tool by name."""
        return self._tools.get(name)
    
    def list_tools(self) -> List[str]:
        """List all registered tool names."""
        return list(self._tools.keys())
    
    def get_tools_for_role(self, tool_names: List[str]) -> List[BaseTool]:
        """Get tools for a specific role."""
        tools = []
        for name in tool_names:
            tool = self.get_tool(name)
            if tool:
                tools.append(tool)
        return tools
    
    def get_tool_schemas(self, tool_names: List[str]) -> List[ToolSchema]:
        """Get schemas for specified tools."""
        schemas = []
        for name in tool_names:
            tool = self.get_tool(name)
            if tool:
                schemas.append(tool.get_schema())
        return schemas


# Global tool registry
tool_registry = ToolRegistry()