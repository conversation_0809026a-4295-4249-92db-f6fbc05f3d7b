from pydantic import BaseModel, Field
from typing import List, Dict, Any


class ToolInfo(BaseModel):
    """Tool information schema."""
    name: str = Field(..., description="Tool name identifier")
    description: str = Field(..., description="Tool description")
    parameters: Dict[str, Any] = Field(..., description="Tool parameters schema")
    required: List[str] = Field(default_factory=list, description="Required parameters")
    category: str = Field(default="general", description="Tool category")
    schema: Dict[str, Any] = Field(..., description="Tool schema")


class ToolListResponse(BaseModel):
    """Response schema for tool listing."""
    tools: List[ToolInfo]
    total: int


class ToolExecutionRequest(BaseModel):
    """Request schema for tool execution."""
    tool_name: str = Field(..., description="Name of the tool to execute")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Tool parameters")


class ToolExecutionResponse(BaseModel):
    """Response schema for tool execution."""
    success: bool
    result: Any = None
    error: str = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
