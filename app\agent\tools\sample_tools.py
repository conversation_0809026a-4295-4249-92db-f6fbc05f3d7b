"""
Sample tools for demonstrating AI function calling capabilities.

This module contains example tools that can be called by the AI
to demonstrate the tool system.
"""

import json
import math
import datetime
from typing import Dict, Any, List

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry


# Calculator tool
class CalculatorTool(BaseTool):
    """Tool for performing basic mathematical calculations."""
    
    @property
    def name(self) -> str:
        return "calculate"
    
    @property
    def description(self) -> str:
        return "Perform basic mathematical calculations"
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression to evaluate (e.g., '2 + 3 * 4')"
                    }
                },
                "required": ["expression"]
            }
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Safely evaluate a mathematical expression.
        
        Args:
            expression: Mathematical expression to evaluate
            
        Returns:
            ToolResult with calculation result
        """
        try:
            expression = kwargs.get("expression", "").strip()
            
            if not expression:
                return ToolResult(
                    success=False,
                    error="No expression provided"
                )
            
            # Only allow safe mathematical operations
            allowed_names = {
                k: v for k, v in math.__dict__.items() 
                if not k.startswith("__")
            }
            allowed_names.update({
                "abs": abs,
                "round": round,
                "min": min,
                "max": max,
                "sum": sum,
                "pow": pow
            })
            
            # Evaluate the expression safely
            result = eval(expression, {"__builtins__": {}}, allowed_names)
            
            return ToolResult(
                success=True,
                result=result,
                metadata={
                    "expression": expression,
                    "operation": "calculation"
                }
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                metadata={"expression": expression}
            )


# Date and time tool
class DateTimeTool(BaseTool):
    """Tool for getting current date and time information."""
    
    @property
    def name(self) -> str:
        return "get_current_time"
    
    @property
    def description(self) -> str:
        return "Get the current date and time"
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "timezone": {
                        "type": "string",
                        "description": "Timezone (optional, defaults to UTC)",
                        "default": "UTC"
                    },
                    "format": {
                        "type": "string",
                        "description": "Date format (optional, defaults to ISO format)",
                        "default": "iso"
                    }
                },
                "required": []
            }
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Get current date and time.
        
        Returns:
            ToolResult with current time information
        """
        try:
            timezone = kwargs.get("timezone", "UTC")
            format_type = kwargs.get("format", "iso")
            
            now = datetime.datetime.utcnow()
            
            if format_type == "iso":
                time_str = now.isoformat() + "Z"
            elif format_type == "readable":
                time_str = now.strftime("%Y-%m-%d %H:%M:%S UTC")
            elif format_type == "timestamp":
                time_str = str(int(now.timestamp()))
            else:
                time_str = now.isoformat() + "Z"
            
            return ToolResult(
                success=True,
                result={
                    "current_time": time_str,
                    "timezone": timezone,
                    "format": format_type,
                    "timestamp": int(now.timestamp())
                },
                metadata={
                    "operation": "get_time",
                    "timezone": timezone
                }
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e)
            )


# Text processing tool
class TextAnalyzerTool(BaseTool):
    """Tool for analyzing text and providing statistics."""
    
    @property
    def name(self) -> str:
        return "analyze_text"
    
    @property
    def description(self) -> str:
        return "Analyze text and provide statistics"
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to analyze"
                    },
                    "include_words": {
                        "type": "boolean",
                        "description": "Include word frequency analysis",
                        "default": False
                    }
                },
                "required": ["text"]
            }
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Analyze text and provide statistics.
        
        Args:
            text: Text to analyze
            include_words: Whether to include word frequency analysis
            
        Returns:
            ToolResult with text analysis results
        """
        try:
            text = kwargs.get("text", "")
            include_words = kwargs.get("include_words", False)
            
            if not text:
                return ToolResult(
                    success=False,
                    error="No text provided"
                )
            
            # Basic statistics
            char_count = len(text)
            word_count = len(text.split())
            line_count = len(text.splitlines())
            
            # Character frequency
            char_freq = {}
            for char in text.lower():
                if char.isalpha():
                    char_freq[char] = char_freq.get(char, 0) + 1
            
            result_data = {
                "character_count": char_count,
                "word_count": word_count,
                "line_count": line_count,
                "character_frequency": char_freq
            }
            
            # Word frequency (optional)
            if include_words:
                words = text.lower().split()
                word_freq = {}
                for word in words:
                    # Remove punctuation
                    clean_word = ''.join(c for c in word if c.isalnum())
                    if clean_word:
                        word_freq[clean_word] = word_freq.get(clean_word, 0) + 1
                
                # Get top 10 most frequent words
                top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
                result_data["word_frequency"] = dict(top_words)
            
            return ToolResult(
                success=True,
                result=result_data,
                metadata={
                    "operation": "text_analysis",
                    "include_words": include_words
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e)
            )


# Data processing tool
class JsonProcessorTool(BaseTool):
    """Tool for processing and validating JSON data."""

    @property
    def name(self) -> str:
        return "process_json"

    @property
    def description(self) -> str:
        return "Process and validate JSON data"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "json_string": {
                        "type": "string",
                        "description": "JSON string to process"
                    },
                    "operation": {
                        "type": "string",
                        "description": "Operation to perform: 'validate', 'pretty', 'minify'",
                        "enum": ["validate", "pretty", "minify"],
                        "default": "validate"
                    }
                },
                "required": ["json_string"]
            }
        )

    async def execute(self, **kwargs) -> ToolResult:
        """
        Process and validate JSON data.

        Args:
            json_string: JSON string to process
            operation: Operation to perform

        Returns:
            ToolResult with processing results
        """
        try:
            json_string = kwargs.get("json_string", "")
            operation = kwargs.get("operation", "validate")

            if not json_string:
                return ToolResult(
                    success=False,
                    error="No JSON string provided"
                )

            # Parse JSON
            data = json.loads(json_string)

            result_data = {
                "valid": True
            }

            if operation == "validate":
                result_data["message"] = "JSON is valid"
                result_data["data"] = data
            elif operation == "pretty":
                result_data["formatted"] = json.dumps(data, indent=2, ensure_ascii=False)
            elif operation == "minify":
                result_data["formatted"] = json.dumps(data, separators=(',', ':'))

            return ToolResult(
                success=True,
                result=result_data,
                metadata={
                    "operation": operation,
                    "valid": True
                }
            )

        except json.JSONDecodeError as e:
            return ToolResult(
                success=False,
                error=f"Invalid JSON: {str(e)}",
                metadata={
                    "operation": operation,
                    "valid": False
                }
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e)
            )


# System information tool
class SystemInfoTool(BaseTool):
    """Tool for getting basic system information."""

    @property
    def name(self) -> str:
        return "get_system_info"

    @property
    def description(self) -> str:
        return "Get basic system information"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {},
                "required": []
            }
        )

    async def execute(self, **kwargs) -> ToolResult:
        """
        Get basic system information.

        Returns:
            ToolResult with system information
        """
        try:
            import platform
            import sys

            return ToolResult(
                success=True,
                result={
                    "python_version": sys.version,
                    "platform": platform.platform(),
                    "architecture": platform.architecture(),
                    "processor": platform.processor()
                },
                metadata={
                    "operation": "system_info"
                }
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e)
            )


# Register tools
tool_registry.register_tool(CalculatorTool())
tool_registry.register_tool(DateTimeTool())
tool_registry.register_tool(TextAnalyzerTool())
tool_registry.register_tool(JsonProcessorTool())
# tool_registry.register_tool(SystemInfoTool())
