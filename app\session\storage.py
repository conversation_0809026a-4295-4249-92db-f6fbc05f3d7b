import json
import redis
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from app.core.config import settings
from app.core.exceptions import SessionError


class RedisSessionStorage:
    """Redis-based session context storage."""
    
    def __init__(self, redis_url: str = None):
        self.redis_url = redis_url or settings.redis_url
        self._redis_client: Optional[redis.Redis] = None
    
    @property
    def redis_client(self) -> redis.Redis:
        """Get Redis client, create if needed."""
        if self._redis_client is None:
            try:
                self._redis_client = redis.from_url(
                    self.redis_url,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
                # Test connection
                self._redis_client.ping()
            except Exception as e:
                raise SessionError(f"Failed to connect to Redis: {str(e)}")
        return self._redis_client
    
    def _get_session_key(self, session_key: str) -> str:
        """Generate Redis key for session context."""
        return f"session:context:{session_key}"
    
    def _get_messages_key(self, session_key: str) -> str:
        """Generate Redis key for session messages."""
        return f"session:messages:{session_key}"
    
    def _get_metadata_key(self, session_key: str) -> str:
        """Generate Redis key for session metadata."""
        return f"session:meta:{session_key}"
    
    def save_session_context(
        self,
        session_key: str,
        context: Dict[str, Any],
        ttl_hours: int = 24
    ) -> bool:
        """
        Save session context to Redis.
        
        Args:
            session_key: Unique session identifier
            context: Session context data
            ttl_hours: Time to live in hours
            
        Returns:
            bool: True if successful
            
        Raises:
            SessionError: If save operation fails
        """
        try:
            redis_key = self._get_session_key(session_key)
            context_data = {
                "context": context,
                "last_updated": datetime.utcnow().isoformat(),
                "session_key": session_key
            }
            
            # Save context with TTL
            self.redis_client.setex(
                redis_key,
                timedelta(hours=ttl_hours),
                json.dumps(context_data)
            )
            
            return True
            
        except Exception as e:
            raise SessionError(f"Failed to save session context: {str(e)}")
    
    def get_session_context(self, session_key: str) -> Optional[Dict[str, Any]]:
        """
        Get session context from Redis.
        
        Args:
            session_key: Unique session identifier
            
        Returns:
            Optional[Dict]: Session context or None if not found
            
        Raises:
            SessionError: If retrieval operation fails
        """
        try:
            redis_key = self._get_session_key(session_key)
            context_json = self.redis_client.get(redis_key)
            
            if not context_json:
                return None
            
            context_data = json.loads(context_json)
            return context_data.get("context", {})
            
        except json.JSONDecodeError:
            raise SessionError("Invalid session context data in Redis")
        except Exception as e:
            raise SessionError(f"Failed to get session context: {str(e)}")
    
    def save_session_messages(
        self,
        session_key: str,
        messages: List[Dict[str, Any]],
        max_messages: int = 50
    ) -> bool:
        """
        Save recent session messages to Redis for quick access.
        
        Args:
            session_key: Unique session identifier
            messages: List of message dictionaries
            max_messages: Maximum number of messages to keep
            
        Returns:
            bool: True if successful
        """
        try:
            redis_key = self._get_messages_key(session_key)
            
            # Keep only recent messages
            recent_messages = messages[-max_messages:] if len(messages) > max_messages else messages
            
            messages_data = {
                "messages": recent_messages,
                "last_updated": datetime.utcnow().isoformat(),
                "count": len(recent_messages)
            }
            
            # Save with 24 hour TTL
            self.redis_client.setex(
                redis_key,
                timedelta(hours=24),
                json.dumps(messages_data)
            )
            
            return True
            
        except Exception as e:
            raise SessionError(f"Failed to save session messages: {str(e)}")
    
    def get_session_messages(self, session_key: str) -> List[Dict[str, Any]]:
        """
        Get recent session messages from Redis.
        
        Args:
            session_key: Unique session identifier
            
        Returns:
            List[Dict]: List of recent messages
        """
        try:
            redis_key = self._get_messages_key(session_key)
            messages_json = self.redis_client.get(redis_key)
            
            if not messages_json:
                return []
            
            messages_data = json.loads(messages_json)
            return messages_data.get("messages", [])
            
        except Exception as e:
            # Return empty list if error, don't fail the request
            return []
    
    def update_session_metadata(
        self,
        session_key: str,
        metadata: Dict[str, Any]
    ) -> bool:
        """
        Update session metadata (last accessed, role, etc.).
        
        Args:
            session_key: Unique session identifier
            metadata: Metadata to save
            
        Returns:
            bool: True if successful
        """
        try:
            redis_key = self._get_metadata_key(session_key)
            metadata_data = {
                **metadata,
                "last_updated": datetime.utcnow().isoformat()
            }
            
            # Save with 7 day TTL
            self.redis_client.setex(
                redis_key,
                timedelta(days=7),
                json.dumps(metadata_data)
            )
            
            return True
            
        except Exception as e:
            raise SessionError(f"Failed to update session metadata: {str(e)}")
    
    async def delete_session_data(self, session_key: str) -> bool:
        """
        Delete all Redis data for a session.
        
        Args:
            session_key: Unique session identifier
            
        Returns:
            bool: True if successful
        """
        try:
            keys_to_delete = [
                self._get_session_key(session_key),
                self._get_messages_key(session_key),
                self._get_metadata_key(session_key)
            ]
            
            # Delete all keys
            deleted_count = self.redis_client.delete(*keys_to_delete)
            return deleted_count > 0
            
        except Exception as e:
            raise SessionError(f"Failed to delete session data: {str(e)}")
    
    async def extend_session_ttl(self, session_key: str, hours: int = 24) -> bool:
        """
        Extend the TTL of session data.
        
        Args:
            session_key: Unique session identifier
            hours: Hours to extend TTL
            
        Returns:
            bool: True if successful
        """
        try:
            keys_to_extend = [
                self._get_session_key(session_key),
                self._get_messages_key(session_key)
            ]
            
            for key in keys_to_extend:
                if self.redis_client.exists(key):
                    self.redis_client.expire(key, timedelta(hours=hours))
            
            return True
            
        except Exception as e:
            raise SessionError(f"Failed to extend session TTL: {str(e)}")


# Global storage instance
session_storage = RedisSessionStorage()