### Project Name
- Multi-User, Multi-Session, Multi-Role AI Agent System

### Project Goal
- Build a Python-based system that supports multiple simultaneous users. Each user can open multiple sessions, and each session can be bound to a predefined AI role.
- Support real-time communication (WebSocket), context isolation, persistent data storage, and use advanced models like the GPT series or Qwen.

### Core Functional Requirements
1.  **User Management**
    -   Registration and Login: Provide a registration interface and generate a JWT token for authentication.
2.  **Session Management**
    -   Create, name, switch, restore, and delete sessions.
3.  **Role System**
    -   Preset various roles (e.g., "Teacher," "Programmer"), each with specific configurations for prompts, tool permissions, knowledge base sources, etc.
4.  **Tool Invocation**
    -   Each role can enable a different set of tools (e.g., mathematical calculations, code execution).
5.  **Real-time Communication**
    -   Support WebSocket for real-time chat interaction.
6.  **Data Persistence**
    -   Use Redis to cache the context of each session; use PostgreSQL to store user, session, and message history information.
7.  **Security**
    -   All API requests must carry a valid JWT token for authentication.
8.  **Deployment**
    -   Use Docker Compose for one-click deployment of the database, Redis, and backend services.

### Technology Stack Requirements
-   Backend Language: Python 3.10+
-   Web Framework: FastAPI
-   Large Model Support: OpenAI GPT-4, Qwen, etc.
-   Database: PostgreSQL, Redis
-   User Authentication: JWT + OAuth2PasswordBearer
-   Tool Modules: Custom (e.g., `math_solver`, `code_executor`)
-   Logging: logging / custom logger
-   Configuration Management: Pydantic Settings
-   Deployment Method: Docker + Docker Compose

### Key Module Descriptions
-   User Authentication Module: Includes registration and login interfaces, using JWT for access control.
-   Session Management Module: Supports creating, restoring, and deleting sessions, and allows naming sessions.
-   Role System: Defines roles through YAML files, including name, description, prompt templates, etc.
-   WebSocket Real-time Chat: Supports streaming output, suitable for web or other clients.
-   Database Integration: Uses PostgreSQL to store user, session, and conversation records, with version management via Alembic.
-   Redis Context Caching: Used for fast loading and updating of each session's state.
-   Tool Module: Each role is bound to a specific set of tools.