from fastapi import APIRouter, Depends

from app.models.user import User
from app.schemas.auth import UserResponse, Token
from app.core.dependencies import get_current_user, create_anonymous_token

router = APIRouter()


@router.get("/verify", response_model=UserResponse)
async def verify_token_and_get_user(
    current_user: User = Depends(get_current_user)
):
    """
    Verify JWT token generated by other applications with the same secret key
    and return user information.

    This endpoint accepts JWT tokens that were generated by external applications
    using the same secret key. If the user doesn't exist in the database,
    a new user will be automatically created based on the token payload.

    Args:
        current_user: The authenticated user, injected by dependency.

    Returns:
        UserResponse: User information corresponding to the token.

    Raises:
        AuthenticationError: If token is invalid or expired
    """
    return current_user


@router.post("/anonymous", response_model=Token)
async def get_anonymous_token():
    """
    Get an anonymous access token for temporary usage.
    This allows users to access the application without registration.

    The anonymous token is valid for 1 hour and creates a temporary user
    in the system that can be used to access application features.

    Returns:
        Token: JWT access token for anonymous user
    """
    access_token = create_anonymous_token()

    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=3600  # 1 hour in seconds
    )