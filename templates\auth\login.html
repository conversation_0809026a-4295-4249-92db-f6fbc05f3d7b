{% extends "base.html" %}

{% block title %}Login - AI Agent System{% endblock %}

{% block extra_head %}
<style>
    .login-container {
        max-width: 400px;
        margin: 2rem auto;
        padding: 2rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .login-header h2 {
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .login-header p {
        color: #666;
        margin: 0;
    }
    
    .auth0-btn {
        width: 100%;
        padding: 12px;
        background: #eb5424;
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        transition: background-color 0.3s;
    }
    
    .auth0-btn:hover {
        background: #d44820;
        color: white;
        text-decoration: none;
    }
    
    .auth0-btn i {
        margin-right: 8px;
    }
    
    .error-alert {
        margin-bottom: 1.5rem;
    }
    
    .anonymous-section {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #eee;
        text-align: center;
    }
    
    .anonymous-btn {
        color: #6c757d;
        text-decoration: none;
        font-size: 14px;
    }
    
    .anonymous-btn:hover {
        color: #495057;
        text-decoration: underline;
    }
    
    .features-list {
        margin-top: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .features-list h5 {
        color: #333;
        margin-bottom: 1rem;
    }
    
    .features-list ul {
        margin: 0;
        padding-left: 1.2rem;
    }
    
    .features-list li {
        color: #666;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="login-container">
        <div class="login-header">
            <h2><i class="fas fa-robot"></i> Welcome</h2>
            <p>Sign in to access the AI Agent System</p>
        </div>
        
        {% if error %}
        <div class="alert alert-danger error-alert" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ error }}
        </div>
        {% endif %}
        
        {% if auth0_configured %}
        <!-- Auth0 Login Button -->
        <a href="/auth/login/auth0" class="auth0-btn">
            <i class="fas fa-sign-in-alt"></i>
            Sign in with Auth0
        </a>
        
        <div class="features-list">
            <h5><i class="fas fa-star me-2"></i>What you can do:</h5>
            <ul>
                <li>Create and manage AI roles</li>
                <li>Test AI agents with different configurations</li>
                <li>Access advanced tools and functions</li>
                <li>Save and manage conversation sessions</li>
                <li>View system health and metrics</li>
            </ul>
        </div>
        
        {% else %}
        <!-- Auth0 Not Configured -->
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Authentication Not Configured</strong><br>
            Auth0 authentication is not set up. Please configure the following environment variables:
            <ul class="mt-2 mb-0">
                <li><code>AUTH0_DOMAIN</code></li>
                <li><code>AUTH0_CLIENT_ID</code></li>
                <li><code>AUTH0_CLIENT_SECRET</code></li>
            </ul>
        </div>
        {% endif %}
        
        <!-- Anonymous Access Section -->
        <div class="anonymous-section">
            <p class="text-muted mb-2">
                <small>Want to try without signing in?</small>
            </p>
            <a href="/test" class="anonymous-btn">
                <i class="fas fa-user-secret me-1"></i>
                Continue as Anonymous User
            </a>
        </div>
    </div>
    
    <!-- Additional Information -->
    <div class="row mt-4">
        <div class="col-md-6 offset-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        About AI Agent System
                    </h5>
                    <p class="card-text text-muted">
                        A powerful platform for creating, testing, and managing AI agents with 
                        customizable roles, tools, and configurations. Built with FastAPI and 
                        modern web technologies.
                    </p>
                    <div class="row text-center mt-3">
                        <div class="col-4">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <p class="small mb-0">Custom Roles</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-tools fa-2x text-success mb-2"></i>
                            <p class="small mb-0">Advanced Tools</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-comments fa-2x text-info mb-2"></i>
                            <p class="small mb-0">Interactive Chat</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('alert-danger')) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            }
        });
    }, 5000);
</script>
{% endblock %}
