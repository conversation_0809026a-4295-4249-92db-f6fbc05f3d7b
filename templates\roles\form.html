{% extends "base.html" %}

{% block title %}{{ 'Edit' if role else 'Create' }} Role - AI Agent System{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-{{ 'edit' if role else 'plus' }} me-2"></i>
                        {{ 'Edit' if role else 'Create' }} Role
                    </h3>
                </div>
                <div class="card-body">
                    <form id="roleForm">
                        <!-- Basic Information -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Role Name *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ role.name if role else '' }}" 
                                       {{ 'readonly' if role else '' }} required>
                                <div class="form-text">Unique identifier (lowercase, alphanumeric, hyphens, underscores)</div>
                            </div>
                            <div class="col-md-6">
                                <label for="display_name" class="form-label">Display Name *</label>
                                <input type="text" class="form-control" id="display_name" name="display_name" 
                                       value="{{ role.display_name if role else '' }}" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="2">{{ role.description if role else '' }}</textarea>
                        </div>

                        <!-- System Prompt -->
                        <div class="mb-3">
                            <label for="system_prompt" class="form-label">System Prompt *</label>
                            <textarea class="form-control" id="system_prompt" name="system_prompt" rows="6" required>{{ role.system_prompt if role else '' }}</textarea>
                            <div class="form-text">Instructions that define the AI agent's behavior and personality</div>
                        </div>

                        <!-- Tools Selection -->
                        <div class="mb-3">
                            <label class="form-label">Available Tools</label>
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h6 class="mb-0">Select tools for this role</h6>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshTools()">
                                                <i class="fas fa-refresh me-1"></i>
                                                Refresh
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <input type="text" class="form-control mb-3" id="toolSearch" placeholder="Search tools..." onkeyup="filterTools()">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="selectAllTools" onchange="toggleAllTools()">
                                                <label class="form-check-label" for="selectAllTools">
                                                    <strong>Select All</strong>
                                                </label>
                                            </div>
                                            <div id="toolCategories">
                                                <!-- Tool categories will be loaded here -->
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div id="toolsList">
                                                <!-- Tools will be loaded here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configuration -->
                        <div class="mb-3">
                            <label class="form-label">Configuration</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="temperature" class="form-label">Temperature</label>
                                            <input type="number" class="form-control" id="temperature" name="temperature" 
                                                   min="0" max="2" step="0.1" value="{{ role.config.temperature if role and role.config.temperature else '0.7' }}">
                                            <div class="form-text">Controls randomness (0.0 = deterministic, 2.0 = very random)</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="max_tokens" class="form-label">Max Tokens</label>
                                            <input type="number" class="form-control" id="max_tokens" name="max_tokens" 
                                                   min="1" max="4096" value="{{ role.config.max_tokens if role and role.config.max_tokens else '1000' }}">
                                            <div class="form-text">Maximum response length</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ 'checked' if not role or role.is_active else '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active Role
                                </label>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="/roles" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Back to Roles
                            </a>
                            <div>
                                {% if role %}
                                <button type="button" class="btn btn-info me-2" onclick="testRole()">
                                    <i class="fas fa-flask me-1"></i>
                                    Test Role
                                </button>
                                {% endif %}
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    {{ 'Update' if role else 'Create' }} Role
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Saving role...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Pass role data to JavaScript
    window.roleData = {{ role | tojson if role else 'null' }};
</script>
<script src="/static/js/role-form.js"></script>
{% endblock %}
