{% extends "base.html" %}

{% block title %}Home - AI Agent System{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <!-- Hero Section -->
            <div class="jumbotron bg-primary text-white rounded p-5 mb-5">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="display-4 fw-bold">
                            <i class="fas fa-robot me-3"></i>
                            AI Agent System
                        </h1>
                        <p class="lead">
                            Multi-User, Multi-Session, Multi-Role AI Agent System with MCP Tool Integration
                        </p>
                        <p class="mb-4">
                            Create, manage, and test AI agents with different roles and capabilities. 
                            Each role can be configured with specific tools and behaviors.
                        </p>
                        <div class="d-flex gap-3">
                            <a href="/roles" class="btn btn-light btn-lg">
                                <i class="fas fa-users me-2"></i>
                                Manage Roles
                            </a>
                            <a href="/test" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-flask me-2"></i>
                                Test Agent
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <i class="fas fa-cogs fa-8x opacity-50"></i>
                    </div>
                </div>
            </div>

            <!-- Features Grid -->
            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-users fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">Role Management</h5>
                            <p class="card-text">
                                Create and customize AI roles with specific personalities, 
                                system prompts, and tool configurations.
                            </p>
                            <a href="/roles" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-1"></i>
                                Manage Roles
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-tools fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">MCP Tools</h5>
                            <p class="card-text">
                                Browse and configure Model Context Protocol (MCP) tools 
                                that extend your AI agents' capabilities.
                            </p>
                            <a href="/tools" class="btn btn-success">
                                <i class="fas fa-arrow-right me-1"></i>
                                View Tools
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-flask fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">Test & Debug</h5>
                            <p class="card-text">
                                Test your AI agents in real-time with different roles 
                                and configurations to ensure optimal performance.
                            </p>
                            <a href="/test" class="btn btn-info">
                                <i class="fas fa-arrow-right me-1"></i>
                                Test Agent
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row g-4 mb-5">
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h3 class="text-primary mb-1" id="roleCount">-</h3>
                            <p class="text-muted mb-0">Active Roles</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h3 class="text-success mb-1" id="toolCount">-</h3>
                            <p class="text-muted mb-0">Available Tools</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h3 class="text-info mb-1" id="sessionCount">-</h3>
                            <p class="text-muted mb-0">Active Sessions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h3 class="text-warning mb-1">
                                <i class="fas fa-heart"></i>
                            </h3>
                            <p class="text-muted mb-0">System Health</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="/roles/create" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    Create New Role
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="/docs" target="_blank" class="btn btn-outline-secondary">
                                    <i class="fas fa-book me-2"></i>
                                    API Documentation
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Load quick stats
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Load roles count
        const rolesResponse = await utils.apiGet('/roles');
        document.getElementById('roleCount').textContent = rolesResponse.total || 0;
        
        // Load tools count
        const toolsResponse = await utils.apiGet('/tools');
        document.getElementById('toolCount').textContent = toolsResponse.total || 0;
        
        // Load sessions count (placeholder)
        document.getElementById('sessionCount').textContent = '0';
        
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
});
</script>
{% endblock %}
