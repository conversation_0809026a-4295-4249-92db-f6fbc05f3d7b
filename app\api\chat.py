from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
import uuid

from app.models.base import get_db
from app.models.user import User
from app.models.session import Session as SessionModel
from app.models.message import Message
from app.schemas.chat import (
    ChatMessage,
    ChatResponse,
    MessageResponse,
    MessageHistoryResponse
)
from app.session.manager import SessionManager
from app.agent.factory import agent_factory
from app.core.dependencies import get_anonymous_user
from app.core.exceptions import NotFoundError, SessionError, LLMError
from app.core.config import settings
from app.llm.provider_factory import get_current_provider_info
from datetime import datetime

router = APIRouter()


@router.post("/", response_model=ChatResponse)
async def send_chat_message(
    message_data: ChatMessage,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Send a chat message and get AI response.

    Args:
        message_data: Chat message data
        current_user: Current user (authenticated or anonymous)
        db: Database session

    Returns:
        ChatResponse: AI response with session info

    Raises:
        ValidationError: If session or role is invalid
        LLMError: If AI response generation fails
    """
    session_manager = SessionManager(db)
    
    # Handle session creation or retrieval
    if message_data.session_key:
        # Use existing session
        session = await session_manager.get_session(
            message_data.session_key, 
            current_user.id
        )
        if not session:
            raise NotFoundError("Session not found")
        
        session_key = message_data.session_key
        role_name = session.role_name
    else:
        # Create new session if role_name is provided
        if not message_data.role_name:
            raise SessionError("Either session_key or role_name must be provided")
        
        # Generate a default session name
        session_name = f"Chat {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}"
        
        from app.schemas.session import SessionCreate
        session_create = SessionCreate(
            name=session_name,
            role_name=message_data.role_name
        )
        
        session = await session_manager.create_session(current_user.id, session_create)
        session_key = session.session_key
        role_name = message_data.role_name
    
    try:
        # Get or create AI agent for this session
        agent = await agent_factory.create_agent(session_key, role_name)
        
        # Generate AI response with better error handling
        try:
            ai_response = await agent.generate_response(message_data.content)
        except Exception as llm_exc:
            # Log the specific LLM error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"LLM response generation failed for session {session_key}: {str(llm_exc)}", exc_info=True)
            raise LLMError(f"Failed to generate AI response: {str(llm_exc)}")
        
        # Save messages to database
        session_id = session.id if isinstance(session.id, uuid.UUID) else uuid.UUID(session.id)
        
        # Get current message count for sequence numbers
        message_count = db.query(Message).filter(
            Message.session_id == session_id
        ).count()
        
        # Save user message
        user_message = Message.create_user_message(
            session_id=session_id,
            content=message_data.content,
            sequence_number=message_count + 1
        )
        db.add(user_message)
        
        # Save AI response
        provider_info = get_current_provider_info()
        ai_message = Message.create_assistant_message(
            session_id=session_id,
            content=ai_response,
            sequence_number=message_count + 2,
            metadata={
                "role": role_name,
                "model": provider_info["model"],
                "provider": provider_info["provider"]
            }
        )
        db.add(ai_message)
        db.commit()
        
        # Update session activity
        await session_manager.update_session_activity(session_key)
        
        return ChatResponse(
            content=ai_response,
            session_key=session_key,
            role_name=role_name,
            message_id=str(ai_message.id),
            metadata={
                "role": role_name,
                "model": provider_info["model"],
                "provider": provider_info["provider"],
                "message_count": message_count + 2
            },
            created_at=ai_message.created_at
        )
        
    except Exception as e:
        db.rollback()
        if isinstance(e, (LLMError, SessionError)):
            raise e
        else:
            raise LLMError(f"Failed to process chat message: {str(e)}")


@router.get("/{session_key}/history", response_model=MessageHistoryResponse)
async def get_message_history(
    session_key: str,
    page: int = 1,
    page_size: int = 50,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Get message history for a session.
    
    Args:
        session_key: Session key
        page: Page number (1-based)
        page_size: Number of messages per page
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        MessageHistoryResponse: Message history with pagination
        
    Raises:
        NotFoundError: If session not found
    """
    session_manager = SessionManager(db)
    
    # Verify session exists and user owns it
    session = await session_manager.get_session(session_key, current_user.id)
    if not session:
        raise NotFoundError("Session not found")
    
    session_id = session.id if isinstance(session.id, uuid.UUID) else uuid.UUID(session.id)
    
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Get total message count
    total = db.query(Message).filter(Message.session_id == session_id).count()
    
    # Get messages for current page
    messages = db.query(Message).filter(
        Message.session_id == session_id
    ).order_by(Message.sequence_number).offset(offset).limit(page_size).all()
    
    return MessageHistoryResponse(
        messages=[MessageResponse.from_orm(msg) for msg in messages],
        session_key=session_key,
        total=total,
        page=page,
        page_size=page_size
    )


@router.delete("/{session_key}/history", status_code=status.HTTP_204_NO_CONTENT)
async def clear_message_history(
    session_key: str,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Clear message history for a session.
    
    Args:
        session_key: Session key
        current_user: Current authenticated user
        db: Database session
        
    Raises:
        NotFoundError: If session not found
    """
    session_manager = SessionManager(db)
    
    # Verify session exists and user owns it
    session = await session_manager.get_session(session_key, current_user.id)
    if not session:
        raise NotFoundError("Session not found")
    
    session_id = session.id if isinstance(session.id, uuid.UUID) else uuid.UUID(session.id)
    
    # Delete all messages for this session
    db.query(Message).filter(Message.session_id == session_id).delete()
    db.commit()
    
    # Clear agent memory if agent exists
    agent = await agent_factory.get_agent(session_key)
    if agent:
        agent.memory.clear_history()
        await agent.memory.save_to_storage()


@router.get("/{session_key}/agent-info")
async def get_agent_info(
    session_key: str,
    current_user: User = Depends(get_anonymous_user),
    db: Session = Depends(get_db)
):
    """
    Get information about the AI agent for a session.
    
    Args:
        session_key: Session key
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dict: Agent information
        
    Raises:
        NotFoundError: If session or agent not found
    """
    session_manager = SessionManager(db)
    
    # Verify session exists and user owns it
    session = await session_manager.get_session(session_key, current_user.id)
    if not session:
        raise NotFoundError("Session not found")
    
    # Get agent info
    agent = await agent_factory.get_agent(session_key)
    if not agent:
        # Create agent to get info
        agent = await agent_factory.create_agent(session_key, session.role_name)
    
    agent_info = agent.get_role_info()
    agent_info.update({
        "session_key": session_key,
        "llm_info": agent.llm_provider.get_model_info()
    })
    
    return agent_info