import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, Boolean, DateTime, Index
from sqlalchemy.dialects.postgresql import UUID, JSON
from .base import Base


class Role(Base):
    """Role model for storing AI role configurations."""
    
    __tablename__ = "roles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(50), unique=True, nullable=False, index=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    system_prompt = Column(Text, nullable=False)
    tools = Column(JSON, default=list)  # List of tool names
    config = Column(JSON, default=dict)  # Configuration like temperature, max_tokens, etc.
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Additional indexes for performance
    __table_args__ = (
        Index('ix_roles_name_active', 'name', 'is_active'),
    )
    
    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}', display_name='{self.display_name}')>"
    
    def to_dict(self):
        """Convert role to dictionary format compatible with RoleConfig."""
        return {
            'display_name': self.display_name,
            'description': self.description,
            'system_prompt': self.system_prompt,
            'tools': self.tools or [],
            'config': self.config or {},
            'is_active': self.is_active
        }
