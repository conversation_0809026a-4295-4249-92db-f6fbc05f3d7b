import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from app.llm.base import <PERSON>MProvider, ChatMessage, FunctionCall, ToolCall
from app.llm.provider_factory import create_llm_provider
from app.agent.role_loader import role_loader, RoleConfig
from app.session.storage import session_storage
from app.agent.tools.tool_manager import tool_manager
from app.core.exceptions import ValidationError, LLMError
from app.core.config import settings

logger = logging.getLogger(__name__)


class AgentMemory:
    """Manages conversation memory for an agent session."""
    
    def __init__(self, session_key: str, max_messages: int = 50):
        self.session_key = session_key
        self.max_messages = max_messages
        self.messages: List[ChatMessage] = []
        self.system_message: Optional[ChatMessage] = None
    
    def set_system_message(self, content: str):
        """Set the system message for the agent."""
        self.system_message = ChatMessage(
            role="system",
            content=content
        )
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """Add a message to the conversation history."""
        message = ChatMessage(
            role=role,
            content=content,
            metadata=metadata or {}
        )
        
        self.messages.append(message)
        
        # Keep only the most recent messages
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]
    
    def get_conversation_messages(self) -> List[ChatMessage]:
        """Get all messages for the LLM, including system message."""
        conversation = []
        
        if self.system_message:
            conversation.append(self.system_message)
        
        conversation.extend(self.messages)
        return conversation
    
    def clear_history(self):
        """Clear conversation history (but keep system message)."""
        self.messages.clear()
    
    async def save_to_storage(self):
        """Save conversation history to storage."""
        try:
            messages_data = [
                {
                    "role": msg.role,
                    "content": msg.content,
                    "metadata": msg.metadata or {},
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                for msg in self.messages
            ]

            session_storage.save_session_messages(
                self.session_key,
                messages_data,
                max_messages=self.max_messages
            )
        except Exception as e:
            logger.error(f"Failed to save messages for session {self.session_key}: {str(e)}")

    async def load_from_storage(self):
        """Load conversation history from storage."""
        try:
            messages_data = session_storage.get_session_messages(self.session_key)

            self.messages.clear()
            for msg_data in messages_data:
                self.messages.append(ChatMessage(
                    role=msg_data["role"],
                    content=msg_data["content"],
                    metadata=msg_data.get("metadata", {})
                ))
        except Exception as e:
            logger.error(f"Failed to load messages for session {self.session_key}: {str(e)}")
            # Continue with empty message history


class AIAgent:
    """AI Agent that combines a role configuration with an LLM provider."""
    
    def __init__(
        self,
        session_key: str,
        role_config: RoleConfig,
        llm_provider: LLMProvider,
        memory: AgentMemory
    ):
        self.session_key = session_key
        self.role_config = role_config
        self.llm_provider = llm_provider
        self.memory = memory
        
        # Set up system message
        self.memory.set_system_message(role_config.system_prompt)
    
    async def generate_response(self, user_message: str) -> str:
        """
        Generate a response to a user message with function calling support.

        Args:
            user_message: The user's input message

        Returns:
            str: The agent's response

        Raises:
            LLMError: If response generation fails
        """
        try:
            # Add user message to memory
            self.memory.add_message("user", user_message)

            # Get conversation messages
            conversation = self.memory.get_conversation_messages()

            # Get available tools for this role
            tools = self._get_tools_for_role()

            # Generate response using LLM with tools
            llm_config = self.role_config.config.copy()
            response = await self.llm_provider.generate_response(
                conversation,
                tools=tools,
                **llm_config
            )

            # Handle function calls if present
            final_response = await self._handle_function_calls(response, conversation, tools, llm_config)

            # Add final assistant response to memory
            self.memory.add_message(
                "assistant",
                final_response,
                metadata=response.metadata
            )

            # Save conversation to storage and update session metadata
            await self._update_session_state()

            return final_response

        except Exception as e:
            raise LLMError(f"Failed to generate response: {str(e)}")

    def _get_tools_for_role(self) -> List[Dict[str, Any]]:
        """Get available tools/functions for this role."""
        if not self.role_config.tools:
            return []

        # Get tool schemas for the role's tools using the unified tool manager
        return tool_manager.get_tools_for_role(self.role_config.tools)

    async def _handle_function_calls(
        self,
        response,
        conversation: List[ChatMessage],
        tools: List[Dict[str, Any]],
        llm_config: Dict[str, Any]
    ) -> str:
        """Handle function calls in the LLM response."""
        # If no function calls, return the content directly
        if not response.tool_calls and not response.function_call:
            return response.content or ""

        # Collect function calls from both formats
        function_calls = []
        if response.function_call:
            function_calls.append(response.function_call)
        if response.tool_calls:
            function_calls.extend([tool_call.function for tool_call in response.tool_calls])

        # Execute all function calls using the unified tool manager
        function_results = []
        for func_call in function_calls:
            import json
            args = json.loads(func_call.arguments) if func_call.arguments else {}
            result = await tool_manager.execute_tool(func_call.name, args)
            function_results.append({
                "name": func_call.name,
                "result": result["result"] if result["success"] else f"Error: {result['error']}",
                "success": result["success"]
            })

        # Build updated conversation with function calls and results
        updated_conversation = conversation.copy()

        # Add assistant message with function call(s)
        updated_conversation.append(ChatMessage(
            role="assistant",
            content=response.content,
            tool_calls=response.tool_calls,
            function_call=response.function_call
        ))

        # Add function/tool responses
        if response.tool_calls:
            # New tool call format
            for tool_call, func_result in zip(response.tool_calls, function_results):
                updated_conversation.append(ChatMessage(
                    role="tool",
                    content=json.dumps(func_result),
                    tool_call_id=tool_call.id,
                    name=func_result["name"]
                ))
        elif response.function_call:
            # Legacy function call format
            updated_conversation.append(ChatMessage(
                role="function",
                content=json.dumps(function_results[0]),
                name=function_results[0]["name"]
            ))

        # Get final response from LLM
        final_response = await self.llm_provider.generate_response(
            updated_conversation,
            tools=tools,
            **llm_config
        )

        return final_response.content or ""

    async def generate_streaming_response(self, user_message: str):
        """
        Generate a streaming response to a user message.

        Args:
            user_message: The user's input message

        Yields:
            str: Streaming response chunks

        Raises:
            LLMError: If response generation fails
        """
        try:
            # Add user message to memory
            self.memory.add_message("user", user_message)

            # Get conversation messages
            conversation = self.memory.get_conversation_messages()

            # Get available tools for this role
            tools = self._get_tools_for_role()

            # Generate streaming response with tools
            llm_config = self.role_config.config.copy()
            full_response = ""

            # Collect tool calls from streaming chunks
            collected_tool_calls = []
            collected_function_call = None
            tool_call_chunks = {}  # For accumulating tool call data across chunks
            current_tool_call_id = None  # Track the current tool call ID

            async for chunk in self.llm_provider.generate_stream(
                conversation,
                tools=tools,
                **llm_config
            ):
                # Handle content chunks
                if chunk.content:
                    full_response += chunk.content
                    yield chunk.content

                # Handle tool calls in streaming chunks
                if chunk.tool_calls:
                    logger.debug(f"Received tool calls in chunk: {chunk.tool_calls}")
                    for tool_call in chunk.tool_calls:
                        logger.debug(f"Processing tool call: id={tool_call.id}, name={tool_call.function.name}, args={tool_call.function.arguments}")

                        # Use the first non-empty ID as the current tool call ID
                        if tool_call.id and not current_tool_call_id:
                            current_tool_call_id = tool_call.id

                        # Use current_tool_call_id for accumulation (handles empty IDs in subsequent chunks)
                        accumulation_id = current_tool_call_id if current_tool_call_id else tool_call.id

                        if accumulation_id not in tool_call_chunks:
                            tool_call_chunks[accumulation_id] = {
                                "id": accumulation_id,
                                "type": tool_call.type,
                                "function": {
                                    "name": "",
                                    "arguments": ""
                                }
                            }

                        # Accumulate function name and arguments
                        if tool_call.function.name:
                            tool_call_chunks[accumulation_id]["function"]["name"] += tool_call.function.name
                            logger.debug(f"Accumulated name: {tool_call_chunks[accumulation_id]['function']['name']}")
                        if tool_call.function.arguments:
                            tool_call_chunks[accumulation_id]["function"]["arguments"] += tool_call.function.arguments
                            logger.debug(f"Accumulated args: {tool_call_chunks[accumulation_id]['function']['arguments']}")

                # Handle legacy function calls
                if chunk.function_call:
                    if not collected_function_call:
                        collected_function_call = {"name": "", "arguments": ""}
                    if chunk.function_call.name:
                        collected_function_call["name"] += chunk.function_call.name
                    if chunk.function_call.arguments:
                        collected_function_call["arguments"] += chunk.function_call.arguments

                if chunk.is_complete:
                    break

            # Process collected tool calls if any
            has_valid_tool_calls = False

            # Convert accumulated tool calls to proper format
            if tool_call_chunks:
                for tool_call_data in tool_call_chunks.values():
                    # Only create tool call if we have a valid function name
                    if tool_call_data["function"]["name"]:
                        collected_tool_calls.append(ToolCall(
                            id=tool_call_data["id"],
                            type=tool_call_data["type"],
                            function=FunctionCall(
                                name=tool_call_data["function"]["name"],
                                arguments=tool_call_data["function"]["arguments"]
                            )
                        ))
                        has_valid_tool_calls = True

            # Check if we have a valid function call
            function_call_obj = None
            if collected_function_call and collected_function_call["name"]:
                function_call_obj = FunctionCall(
                    name=collected_function_call["name"],
                    arguments=collected_function_call["arguments"]
                )
                has_valid_tool_calls = True

            # Only process tool calls if we have valid ones
            if has_valid_tool_calls:
                logger.debug(f"Processing {len(collected_tool_calls)} tool calls")

                # Add assistant message with tool calls to conversation
                from app.llm.base import ChatMessage
                conversation.append(ChatMessage(
                    role="assistant",
                    content=full_response,
                    tool_calls=collected_tool_calls
                ))

                # Execute tool calls and add results to conversation
                for tool_call in collected_tool_calls:
                    try:
                        import json
                        args = json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                        result = await tool_manager.execute_tool(tool_call.function.name, args)

                        # Add tool result to conversation
                        conversation.append(ChatMessage(
                            role="tool",
                            content=json.dumps({
                                "name": tool_call.function.name,
                                "result": result["result"] if result["success"] else f"Error: {result['error']}",
                                "success": result["success"]
                            }),
                            tool_call_id=tool_call.id,
                            name=tool_call.function.name
                        ))

                    except Exception as e:
                        logger.error(f"Error executing tool {tool_call.function.name}: {e}")
                        # Add error result to conversation
                        conversation.append(ChatMessage(
                            role="tool",
                            content=json.dumps({
                                "name": tool_call.function.name,
                                "result": f"Error: {str(e)}",
                                "success": False
                            }),
                            tool_call_id=tool_call.id,
                            name=tool_call.function.name
                        ))

                # Generate and stream final response after tool execution
                logger.debug("Generating final response after tool execution")
                try:
                    chunk_count = 0
                    # Don't pass tools or tool_choice for final response - we want text generation only
                    final_config = llm_config.copy()
                    async for chunk in self.llm_provider.generate_stream(
                        conversation,
                        **final_config
                    ):
                        chunk_count += 1
                        logger.debug(f"Final response chunk #{chunk_count}: {chunk.content}")
                        if chunk.content:
                            yield chunk.content
                            full_response += chunk.content
                    logger.debug(f"Final response streaming complete. Total chunks: {chunk_count}")
                except Exception as e:
                    logger.error(f"Error in final response streaming: {e}")
                    raise

            # Add complete assistant response to memory
            if full_response:
                self.memory.add_message(
                    "assistant",
                    full_response,
                    metadata={"streaming": True}
                )

                # Save conversation to storage and update session metadata
                await self._update_session_state()

        except Exception as e:
            raise LLMError(f"Failed to generate streaming response: {str(e)}")

    async def _update_session_state(self):
        """Update session state in storage."""
        try:
            await self.memory.save_to_storage()
            session_storage.update_session_metadata(self.session_key, {
                "last_activity": datetime.now(timezone.utc).isoformat(),
                "message_count": len(self.memory.messages)
            })
        except Exception as e:
            logger.error(f"Failed to update session state for {self.session_key}: {str(e)}")

    def get_role_info(self) -> Dict[str, Any]:
        """Get information about the agent's role."""
        return {
            "role_name": self.role_config.display_name,
            "description": self.role_config.description,
            "tools": self.role_config.tools or [],
            "message_count": len(self.memory.messages)
        }


class AgentFactory:
    """Factory for creating AI agents with specific roles."""

    def __init__(self):
        self._active_agents: Dict[str, AIAgent] = {}
        self._role_cache: Dict[str, RoleConfig] = {}
    
    async def create_agent(
        self,
        session_key: str,
        role_name: str,
        llm_provider: Optional[LLMProvider] = None
    ) -> AIAgent:
        """
        Create or retrieve an AI agent for a session.
        
        Args:
            session_key: Unique session identifier
            role_name: Name of the role to assign
            llm_provider: Optional custom LLM provider
            
        Returns:
            AIAgent: Configured AI agent
            
        Raises:
            ValidationError: If role doesn't exist
            LLMError: If LLM provider setup fails
        """
        # Check if agent already exists for this session
        if session_key in self._active_agents:
            return self._active_agents[session_key]
        
        # Get role configuration (with caching)
        role_config = self._get_role_config(role_name)
        if not role_config or not role_config.is_active:
            raise ValidationError(f"Role '{role_name}' not found or inactive")
        
        # Create LLM provider if not provided
        if llm_provider is None:
            llm_provider = create_llm_provider(config=role_config.config)

        # Validate LLM connection (log warnings but don't fail agent creation)
        try:
            connection_valid = await llm_provider.validate_connection()
            if not connection_valid:
                logger.warning(f"LLM connection validation failed for session {session_key}")
        except Exception as e:
            logger.warning(f"LLM connection validation error for session {session_key}: {str(e)}")
        
        # Create memory manager
        memory = AgentMemory(
            session_key=session_key,
            max_messages=settings.max_messages_per_session
        )
        
        # Load existing conversation from storage
        await memory.load_from_storage()
        
        # Create agent
        agent = AIAgent(
            session_key=session_key,
            role_config=role_config,
            llm_provider=llm_provider,
            memory=memory
        )
        
        # Cache the agent
        self._active_agents[session_key] = agent
        
        return agent
    
    async def get_agent(self, session_key: str) -> Optional[AIAgent]:
        """Get an existing agent for a session."""
        return self._active_agents.get(session_key)
    
    async def remove_agent(self, session_key: str) -> bool:
        """Remove an agent from memory (usually when session is deleted)."""
        if session_key in self._active_agents:
            del self._active_agents[session_key]
            return True
        return False
    
    def get_active_sessions(self) -> List[str]:
        """Get list of session keys with active agents."""
        return list(self._active_agents.keys())
    
    def _get_role_config(self, role_name: str) -> Optional[RoleConfig]:
        """Get role configuration with caching."""
        if role_name not in self._role_cache:
            self._role_cache[role_name] = role_loader.get_role(role_name)
        return self._role_cache[role_name]

    def clear_role_cache(self):
        """Clear the role configuration cache."""
        self._role_cache.clear()

    async def cleanup_inactive_agents(self, active_session_keys: List[str]):
        """Remove agents for sessions that are no longer active."""
        current_sessions = set(self._active_agents.keys())
        active_sessions = set(active_session_keys)

        inactive_sessions = current_sessions - active_sessions

        for session_key in inactive_sessions:
            await self.remove_agent(session_key)

        logger.info(f"Cleaned up {len(inactive_sessions)} inactive agents")


# Global agent factory instance
agent_factory = AgentFactory()